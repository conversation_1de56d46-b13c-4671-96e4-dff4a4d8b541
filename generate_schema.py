#!/usr/bin/env python3
"""
Script to generate event_schema.json from events schema.csv
"""

import csv
import json
import re

def infer_data_type(prop_name):
    """Infer data type based on property name patterns."""
    prop_lower = prop_name.lower()
    
    # ID fields
    if prop_name.endswith('.id') or prop_name == 'id':
        return {
            "type": "integer",
            "range": {"min": 1, "max": 9999}
        }
    
    # Datetime fields
    if prop_name in ['timestamp', 'start', 'end', 'startAt', 'endAt', 'start_at', 'end_at']:
        return {
            "type": "datetime",
            "format": "iso"
        }
    
    # Duration fields
    if 'duration' in prop_lower and 'seconds' in prop_lower:
        return {
            "type": "integer",
            "range": {"min": 1, "max": 3600},
            "description": "Duration in seconds"
        }
    
    # Boolean fields
    if prop_name in ['showHidden', 'recommended'] or 'hidden' in prop_lower or 'show' in prop_lower:
        return {
            "type": "boolean"
        }
    
    # Status fields
    if prop_name in ['status', 'state']:
        return {
            "type": "string",
            "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]
        }
    
    # Page name fields
    if 'pagename' in prop_lower or prop_name == 'pageName':
        return {
            "type": "string",
            "options": ["Dashboard", "Device Dashboard", "Workflow Editor", "Audit Trail", 
                       "Resources", "Settings", "User Management", "Reports", "Analytics", 
                       "Notifications", "Devices", "Connectors", "Workflows"]
        }
    
    # User type fields
    if 'usertype' in prop_lower or prop_name == 'userType':
        return {
            "type": "string",
            "options": ["admin", "user", "viewer", "editor", "manager", "operator"]
        }
    
    # Order fields
    if prop_name in ['order', 'orderBy']:
        if prop_name == 'order':
            return {
                "type": "string",
                "options": ["asc", "desc"]
            }
        else:  # orderBy
            return {
                "type": "string",
                "options": ["name", "date", "id", "status", "type", "created_at", "updated_at"]
            }
    
    # Array fields (indicated by .$)
    if prop_name.endswith('.$'):
        base_name = prop_name[:-2]
        if 'status' in base_name.lower():
            return {
                "type": "array",
                "items": {
                    "type": "string",
                    "options": ["active", "inactive", "pending", "completed"]
                }
            }
        elif 'type' in base_name.lower():
            return {
                "type": "array", 
                "items": {
                    "type": "string",
                    "options": ["device", "connector", "workflow", "dashboard", "user", "group"]
                }
            }
        else:
            return {
                "type": "array",
                "items": {"type": "string"}
            }
    
    # Device/connector/numeric references
    if prop_name in ['device', 'connector'] and not '.' in prop_name:
        return {
            "type": "integer",
            "range": {"min": 1000, "max": 9999}
        }
    
    # Search/filter fields
    if prop_name in ['search', 'contains', 'key', 'resource']:
        return {
            "type": "string",
            "description": f"Search or filter {prop_name}"
        }
    
    # Smart view ID
    if 'smartviewid' in prop_lower:
        return {
            "type": "string",
            "pattern": "uuid"
        }
    
    # Default to string
    return {
        "type": "string"
    }

def add_custom_properties(events_schema):
    """Add custom properties for specific event types."""

    # Add 'name' property for role and group events
    role_group_events = ['role.create', 'role.update', 'group.create', 'group.update']
    for event_name in role_group_events:
        if event_name in events_schema:
            if 'role' in event_name:
                events_schema[event_name]["properties"]["name"] = {
                    "type": "string",
                    "description": "Role name",
                    "options": ["Admin", "User", "Manager", "Viewer", "Editor", "Operator", "Developer", "Analyst"]
                }
            else:  # group events
                events_schema[event_name]["properties"]["name"] = {
                    "type": "string",
                    "description": "Group name",
                    "options": ["Development Team", "QA Team", "Operations Team", "Analytics Team", "Admin Group", "User Group", "Test Group", "Production Team"]
                }

    # Add device type properties for device-related events
    device_events = ['port.binding.create', 'port.binding.destroy', 'device.command.create', 'device.create', 'device.data.export']
    for event_name in device_events:
        if event_name in events_schema:
            # Add type_id to device object
            events_schema[event_name]["properties"]["device.type_id"] = {
                "type": "integer",
                "range": {"min": 1000, "max": 9999},
                "description": "Device type ID"
            }
            # Add type_name to device object
            events_schema[event_name]["properties"]["device.type_name"] = {
                "type": "string",
                "description": "Device type name",
                "options": ["Temperature Sensor", "Pressure Sensor", "Flow Meter", "pH Sensor", "Conductivity Meter",
                           "Turbidity Sensor", "Level Sensor", "Valve Controller", "Pump Controller", "Data Logger"]
            }

    return events_schema

def parse_csv_to_schema(csv_file_path):
    """Parse CSV and create JSON schema."""
    events_schema = {}

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            event_name = row['Event Name']
            property_name = row['Property Name']

            # Skip empty properties and the generic "properties" entry
            if property_name.strip() == "" or property_name == "properties":
                continue

            # Remove "properties." prefix if present
            if property_name.startswith('properties.'):
                property_name = property_name[11:]

            if event_name not in events_schema:
                events_schema[event_name] = {
                    "description": f"Event: {event_name}",
                    "properties": {}
                }

            # Infer data type
            data_type_info = infer_data_type(property_name)
            events_schema[event_name]["properties"][property_name] = data_type_info

    # Add custom properties for specific events
    events_schema = add_custom_properties(events_schema)

    return {
        "schema_version": "1.0",
        "generated_from": "events schema.csv",
        "events": events_schema
    }

if __name__ == "__main__":
    schema = parse_csv_to_schema("events schema.csv")
    
    with open("event_schema.json", "w", encoding="utf-8") as f:
        json.dump(schema, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Generated event_schema.json with {len(schema['events'])} event types")
    
    # Print summary
    total_properties = sum(len(event['properties']) for event in schema['events'].values())
    print(f"📊 Total properties: {total_properties}")
    
    # Show data type distribution
    type_counts = {}
    for event in schema['events'].values():
        for prop in event['properties'].values():
            prop_type = prop['type']
            type_counts[prop_type] = type_counts.get(prop_type, 0) + 1
    
    print("📈 Data type distribution:")
    for dtype, count in sorted(type_counts.items()):
        print(f"   - {dtype}: {count}")
