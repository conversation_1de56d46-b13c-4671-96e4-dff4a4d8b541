#!/usr/bin/env python3
"""
Test specific events with new properties
"""

import json
import uuid
from app import load_events_schema, build_properties_from_schema, track

def test_specific_events():
    """Test specific events that should have new properties"""
    schema = load_events_schema("event_schema.json")
    
    # Test role.create
    print("🧪 Testing role.create event:")
    if "role.create" in schema:
        user_id = str(uuid.uuid4())
        role_props = build_properties_from_schema(schema["role.create"])
        print("Generated properties:")
        print(json.dumps(role_props, indent=2))
        
        print(f"\nSending role.create event...")
        try:
            status = track("role.create", user_id=user_id, properties=role_props)
            print(f"✅ Sent successfully (Status: {status})")
        except Exception as e:
            print(f"❌ Failed: {e}")
    
    # Test group.update
    print("\n🧪 Testing group.update event:")
    if "group.update" in schema:
        user_id = str(uuid.uuid4())
        group_props = build_properties_from_schema(schema["group.update"])
        print("Generated properties:")
        print(json.dumps(group_props, indent=2))
        
        print(f"\nSending group.update event...")
        try:
            status = track("group.update", user_id=user_id, properties=group_props)
            print(f"✅ Sent successfully (Status: {status})")
        except Exception as e:
            print(f"❌ Failed: {e}")
    
    # Test device.command.create
    print("\n🧪 Testing device.command.create event:")
    if "device.command.create" in schema:
        user_id = str(uuid.uuid4())
        device_props = build_properties_from_schema(schema["device.command.create"])
        print("Generated properties:")
        print(json.dumps(device_props, indent=2))
        
        print(f"\nSending device.command.create event...")
        try:
            status = track("device.command.create", user_id=user_id, properties=device_props)
            print(f"✅ Sent successfully (Status: {status})")
        except Exception as e:
            print(f"❌ Failed: {e}")

if __name__ == "__main__":
    print("🚀 Testing specific events with new properties...\n")
    test_specific_events()
    print("\n🎉 Test completed!")
