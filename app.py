import os, requests, datetime, dotenv, json

# Example synthetic events for LO_Web_Test_Labrcp_Roche_v2

dotenv.load_dotenv()

BASE_URL = os.getenv("SEGMENT_BASE_URL", "https://api.segment.io")
WRITE_KEY = os.getenv("SEGMENT_WRITE_KEY")  # required
AUTH = (WRITE_KEY, "")  # Basic Auth: username=write key, password empty

def iso_now():
    return datetime.datetime.utcnow().replace(microsecond=0).isoformat() + "Z"

def track(event, user_id=None, anonymous_id=None, properties=None, timestamp=None):
    if not (user_id or anonymous_id):
        raise ValueError("Provide user_id or anonymous_id")
    payload = {
        "event": event,
        "properties": properties or {},
        **({"userId": user_id} if user_id else {}),
        **({"anonymousId": anonymous_id} if anonymous_id else {}),
        **({"timestamp": timestamp or iso_now()}),
    }
    r = requests.post(f"{BASE_URL}/v1/track", json=payload, auth=AUTH, timeout=10)
    r.raise_for_status()
    return r.status_code

if __name__ == "__main__":
    print("Sending track…")
    print(track(
        event="Lab Test Created",
        user_id="test_user_123",
        properties={
            "properties": {
                "labId": "LRC-42", 
                "assay": "PCR", 
                "priority": "high"
        }
        },
    ))
    print("Done.")
