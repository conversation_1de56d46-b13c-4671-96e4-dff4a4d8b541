import os, requests, datetime, dotenv, json, csv, random, uuid
from faker import Faker

# Example synthetic events for LO_Web_Test_Labrcp_Roche

dotenv.load_dotenv()

BASE_URL = os.getenv("SEGMENT_BASE_URL", "https://api.segment.io")
WRITE_KEY = os.getenv("SEGMENT_WRITE_KEY")  # required
AUTH = (WRITE_KEY, "")  # Basic Auth: username=write key, password empty

def iso_now():
    return datetime.datetime.utcnow().replace(microsecond=0).isoformat() + "Z"

# Initialize Faker for generating random data
fake = Faker()

def parse_events_schema(csv_file_path):
    """Parse the events schema CSV and group properties by event name."""
    events_schema = {}

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            event_name = row['Event Name']
            property_name = row['Property Name']

            # Skip empty properties (marked as " ")
            if property_name.strip() == "":
                continue

            if event_name not in events_schema:
                events_schema[event_name] = []

            events_schema[event_name].append(property_name)

    return events_schema

def infer_property_type(prop_name):
    """Infer the data type based on property name patterns."""
    prop_lower = prop_name.lower()

    if prop_name.endswith('.id'):
        return 'id'
    elif prop_name in ['timestamp', 'start', 'end', 'startAt', 'endAt']:
        return 'datetime'
    elif 'duration' in prop_lower and 'seconds' in prop_lower:
        return 'duration'
    elif prop_name == 'status':
        return 'status'
    elif 'pagename' in prop_lower:
        return 'page_name'
    elif prop_name == 'state':
        return 'state'
    elif '.' in prop_name:
        return 'nested_object'
    else:
        return 'string'

def generate_random_value(prop_name, prop_type):
    """Generate random values based on property type."""
    if prop_type == 'id':
        return random.randint(1, 9999)
    elif prop_type == 'datetime':
        # Generate recent timestamps
        return fake.date_time_between(start_date='-7d', end_date='now').isoformat() + 'Z'
    elif prop_type == 'duration':
        return random.randint(1, 3600)  # 1 second to 1 hour
    elif prop_type == 'status':
        return random.choice(['active', 'pending', 'completed', 'failed', 'success'])
    elif prop_type == 'state':
        return random.choice(['active', 'inactive', 'pending', 'completed', 'draft'])
    elif prop_type == 'page_name':
        return random.choice([
            'Dashboard', 'Device Dashboard', 'Workflow Editor', 'Audit Trail',
            'Resources', 'Settings', 'User Management', 'Reports', 'Analytics'
        ])
    else:
        return fake.word().capitalize()

def build_nested_properties(property_names):
    """Build nested property structure from dot-notation property names."""
    result = {}

    for prop_name in property_names:
        prop_type = infer_property_type(prop_name)

        if '.' in prop_name:
            # Handle nested properties like "organisation.id"
            parts = prop_name.split('.')
            current = result

            # Navigate/create nested structure
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                elif not isinstance(current[part], dict):
                    # If the parent is not a dict, we need to convert it
                    # This handles cases where we have both "user" and "user.id"
                    current[part] = {}
                current = current[part]

            # Set the final value
            final_key = parts[-1]
            if final_key == 'id':
                current[final_key] = generate_random_value(prop_name, 'id')
            else:
                current[final_key] = generate_random_value(prop_name, prop_type)
        else:
            # Handle top-level properties
            # Only set if it doesn't already exist as a nested object
            if prop_name not in result:
                result[prop_name] = generate_random_value(prop_name, prop_type)
            elif not isinstance(result[prop_name], dict):
                # If it exists but is not a dict, replace it
                result[prop_name] = generate_random_value(prop_name, prop_type)

    return result

def generate_synthetic_events(schema_file_path, events_per_type=3):
    """Generate synthetic events for all event types in the schema."""
    events_schema = parse_events_schema(schema_file_path)
    generated_events = []

    print(f"Found {len(events_schema)} event types in schema")

    for event_name, properties in events_schema.items():
        print(f"\nGenerating {events_per_type} events for: {event_name}")

        for i in range(events_per_type):
            # Generate random user ID for this event
            user_id = str(uuid.uuid4())

            # Build properties structure
            event_properties = build_nested_properties(properties)

            # Track the event
            try:
                print(f"  Event {i+1}/{events_per_type}:")
                status_code = track(
                    event=event_name,
                    user_id=user_id,
                    properties=event_properties
                )
                generated_events.append({
                    'event': event_name,
                    'user_id': user_id,
                    'properties': event_properties,
                    'status_code': status_code
                })
                print(f"    ✓ Sent successfully (Status: {status_code})")
            except Exception as e:
                print(f"    ✗ Failed to send: {e}")

    return generated_events

def track(event, user_id=None, anonymous_id=None, properties=None, timestamp=None):
    if not (user_id or anonymous_id):
        raise ValueError("Provide user_id or anonymous_id")

    # Create the nested properties structure
    nested_properties = {
        "properties": properties or {},
        "user_id": user_id
    }

    payload = {
        "event": event,
        **({"userId": user_id} if user_id else {}),
        **({"anonymousId": anonymous_id} if anonymous_id else {}),
        "timestamp": timestamp or iso_now(),
        "properties": nested_properties
    }
    print(json.dumps(payload, indent=2))
    r = requests.post(f"{BASE_URL}/v1/track", json=payload, auth=AUTH, timeout=10)
    r.raise_for_status()
    return r.status_code

if __name__ == "__main__":
    print("🚀 Starting synthetic event generation...")

    # Set seed for reproducible results during testing
    fake.seed_instance(12345)
    random.seed(12345)

    # Generate synthetic events from schema
    schema_file = "events schema.csv"

    try:
        generated_events = generate_synthetic_events(schema_file, events_per_type=6)
        print(f"\n✅ Successfully generated {len(generated_events)} synthetic events!")

        # Summary
        event_types = set(event['event'] for event in generated_events)
        print(f"📊 Event types generated: {len(event_types)}")
        for event_type in sorted(event_types):
            count = sum(1 for e in generated_events if e['event'] == event_type)
            print(f"   - {event_type}: {count} events")

    except FileNotFoundError:
        print(f"❌ Schema file '{schema_file}' not found!")
        print("📝 Falling back to single example event...")

        # Fallback example
        print(track(
            event="Device Dashboard View",
            user_id="30bcffa6-ec64-4925-9a1e-f94429164e3f",
            properties={
                "device": 3804,
                "durationSeconds": 3,
                "end": "2025-08-21T09:58:31.739Z",
                "organization": {
                    "id": 32
                },
                "pageName": "Device Dashboard",
                "start": "2025-08-21T09:58:29.077Z",
                "timestamp": "2025-08-21T09:58:31.739Z"
            },
        ))

    print("\n🎉 Done!")
