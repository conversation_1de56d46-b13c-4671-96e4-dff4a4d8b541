import os, requests, datetime, dotenv, json, csv, random, uuid
from faker import Faker

# Example synthetic events for LO_Web_Test_Labrcp_Roche

dotenv.load_dotenv()

BASE_URL = os.getenv("SEGMENT_BASE_URL", "https://api.segment.io")
WRITE_KEY = os.getenv("SEGMENT_WRITE_KEY")  # required
AUTH = (WRITE_KEY, "")  # Basic Auth: username=write key, password empty

def iso_now():
    return datetime.datetime.utcnow().replace(microsecond=0).isoformat() + "Z"

# Initialize Faker for generating random data
fake = Faker()

def load_events_schema(json_file_path):
    """Load the events schema from JSON file."""
    with open(json_file_path, 'r', encoding='utf-8') as file:
        schema = json.load(file)
    return schema['events']

def generate_value_from_schema(prop_schema):
    """Generate random value based on schema definition."""
    prop_type = prop_schema['type']

    if prop_type == 'integer':
        if 'range' in prop_schema:
            return random.randint(prop_schema['range']['min'], prop_schema['range']['max'])
        return random.randint(1, 9999)

    elif prop_type == 'datetime':
        # Generate recent timestamps
        return fake.date_time_between(start_date='-7d', end_date='now').isoformat() + 'Z'

    elif prop_type == 'boolean':
        return random.choice([True, False])

    elif prop_type == 'string':
        if 'options' in prop_schema:
            return random.choice(prop_schema['options'])
        elif 'pattern' in prop_schema and prop_schema['pattern'] == 'uuid':
            return str(uuid.uuid4())
        else:
            return fake.word().capitalize()

    elif prop_type == 'array':
        if 'items' in prop_schema:
            items_schema = prop_schema['items']
            # Generate 1-3 array items
            array_size = random.randint(1, 3)
            return [generate_value_from_schema(items_schema) for _ in range(array_size)]
        return [fake.word() for _ in range(random.randint(1, 3))]

    else:
        return fake.word().capitalize()

def build_properties_from_schema(event_schema):
    """Build properties structure from event schema definition."""
    result = {}
    properties_def = event_schema['properties']

    for prop_name, prop_schema in properties_def.items():
        if '.' in prop_name:
            # Handle nested properties like "organization.id", "device.type_id"
            parts = prop_name.split('.')
            current = result

            # Navigate/create nested structure
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                elif not isinstance(current[part], dict):
                    # If the parent is not a dict, we need to convert it
                    # This handles cases where we have both "user" and "user.id"
                    current[part] = {}
                current = current[part]

            # Set the final value
            final_key = parts[-1]
            current[final_key] = generate_value_from_schema(prop_schema)
        else:
            # Handle top-level properties
            # Only set if it doesn't already exist as a nested object
            if prop_name not in result:
                result[prop_name] = generate_value_from_schema(prop_schema)
            elif not isinstance(result[prop_name], dict):
                # If it exists but is not a dict, replace it
                result[prop_name] = generate_value_from_schema(prop_schema)

    return result

def generate_synthetic_events(schema_file_path, events_per_type=3):
    """Generate synthetic events for all event types in the schema."""
    events_schema = load_events_schema(schema_file_path)
    generated_events = []

    print(f"Found {len(events_schema)} event types in schema")

    for event_name, event_schema in events_schema.items():
        print(f"\nGenerating {events_per_type} events for: {event_name}")

        for i in range(events_per_type):
            # Generate random user ID for this event
            user_id = str(uuid.uuid4())

            # Build properties structure from schema
            event_properties = build_properties_from_schema(event_schema)

            # Track the event
            try:
                print(f"  Event {i+1}/{events_per_type}:")
                status_code = track(
                    event=event_name,
                    user_id=user_id,
                    properties=event_properties
                )
                generated_events.append({
                    'event': event_name,
                    'user_id': user_id,
                    'properties': event_properties,
                    'status_code': status_code
                })
                print(f"    ✓ Sent successfully (Status: {status_code})")
            except Exception as e:
                print(f"    ✗ Failed to send: {e}")

    return generated_events

def track(event, user_id=None, anonymous_id=None, properties=None, timestamp=None):
    if not (user_id or anonymous_id):
        raise ValueError("Provide user_id or anonymous_id")

    # Create the nested properties structure
    nested_properties = {
        "properties": properties or {},
        "user_id": user_id
    }

    payload = {
        "event": event,
        **({"userId": user_id} if user_id else {}),
        **({"anonymousId": anonymous_id} if anonymous_id else {}),
        "timestamp": timestamp or iso_now(),
        "properties": nested_properties
    }
    print(json.dumps(payload, indent=2))
    r = requests.post(f"{BASE_URL}/v1/track", json=payload, auth=AUTH, timeout=10)
    r.raise_for_status()
    return r.status_code

if __name__ == "__main__":
    print("🚀 Starting synthetic event generation...")

    # Set seed for reproducible results during testing
    fake.seed_instance(12345)
    random.seed(12345)

    # Generate synthetic events from schema
    schema_file = "event_schema.json"

    try:
        generated_events = generate_synthetic_events(schema_file, events_per_type=1)
        print(f"\n✅ Successfully generated {len(generated_events)} synthetic events!")

        # Summary
        event_types = set(event['event'] for event in generated_events)
        print(f"📊 Event types generated: {len(event_types)}")
        for event_type in sorted(event_types):
            count = sum(1 for e in generated_events if e['event'] == event_type)
            print(f"   - {event_type}: {count} events")

    except FileNotFoundError:
        print(f"❌ Schema file '{schema_file}' not found!")
        print("📝 Falling back to single example event...")

        # Fallback example
        print(track(
            event="Device Dashboard View",
            user_id="30bcffa6-ec64-4925-9a1e-f94429164e3f",
            properties={
                "device": 3804,
                "durationSeconds": 3,
                "end": "2025-08-21T09:58:31.739Z",
                "organization": {
                    "id": 32
                },
                "pageName": "Device Dashboard",
                "start": "2025-08-21T09:58:29.077Z",
                "timestamp": "2025-08-21T09:58:31.739Z"
            },
        ))

    print("\n🎉 Done!")
