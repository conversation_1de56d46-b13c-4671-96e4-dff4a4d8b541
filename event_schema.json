{"schema_version": "1.0", "generated_from": "events schema.csv", "events": {"Audit Trail filtered": {"description": "Event: Audit Trail filtered", "properties": {"attributes": {"type": "string"}, "endAt": {"type": "datetime", "format": "iso"}, "key": {"type": "string", "description": "Search or filter key"}, "order": {"type": "string", "options": ["asc", "desc"]}, "orderBy": {"type": "string", "options": ["name", "date", "id", "status", "type", "created_at", "updated_at"]}, "organization": {"type": "string"}, "organization.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "resource": {"type": "string", "description": "Search or filter resource"}, "search": {"type": "string", "description": "Search or filter search"}, "smartViewId": {"type": "string", "pattern": "uuid"}, "startAt": {"type": "datetime", "format": "iso"}, "timestamp": {"type": "datetime", "format": "iso"}, "userId": {"type": "string"}, "userType": {"type": "string", "options": ["admin", "user", "viewer", "editor", "manager", "operator"]}, "user_id": {"type": "string"}}}, "Resources Filtered": {"description": "Event: Resources Filtered", "properties": {"contains": {"type": "string", "description": "Search or filter contains"}, "customAttributes": {"type": "string"}, "customAttributes.$": {"type": "array", "items": {"type": "string"}}, "customAttributes.$.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "customAttributes.$.value": {"type": "string"}, "endAt": {"type": "datetime", "format": "iso"}, "order": {"type": "string", "options": ["asc", "desc"]}, "orderBy": {"type": "string", "options": ["name", "date", "id", "status", "type", "created_at", "updated_at"]}, "organization": {"type": "string"}, "organization.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "recommended": {"type": "boolean"}, "showHidden": {"type": "boolean"}, "smartViewId": {"type": "string", "pattern": "uuid"}, "startAt": {"type": "datetime", "format": "iso"}, "statuses": {"type": "string"}, "statuses.$": {"type": "array", "items": {"type": "string", "options": ["active", "inactive", "pending", "completed"]}}, "timestamp": {"type": "datetime", "format": "iso"}, "types": {"type": "string"}, "types.$": {"type": "array", "items": {"type": "string", "options": ["device", "connector", "workflow", "dashboard", "user", "group"]}}, "user_id": {"type": "string"}}}, "Time on Page": {"description": "Event: Time on Page", "properties": {"connector": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "durationSeconds": {"type": "integer", "range": {"min": 1, "max": 3600}, "description": "Duration in seconds"}, "end": {"type": "datetime", "format": "iso"}, "organization": {"type": "string"}, "organization.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "pageName": {"type": "string", "options": ["Dashboard", "<PERSON>ce Dashboard", "Workflow Editor", "Audit Trail", "Resources", "Settings", "User Management", "Reports", "Analytics", "Notifications", "Devices", "Connectors", "Workflows"]}, "start": {"type": "datetime", "format": "iso"}, "timestamp": {"type": "datetime", "format": "iso"}, "workflow_run": {"type": "string"}, "workflow_template": {"type": "string"}, "user_id": {"type": "string"}}}, "automation.create": {"description": "Event: automation.create", "properties": {"automation": {"type": "string"}, "automation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "automation.update": {"description": "Event: automation.update", "properties": {"automation": {"type": "string"}, "automation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "connector.create": {"description": "Event: connector.create", "properties": {"connector": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "connector.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "connector.destroy": {"description": "Event: connector.destroy", "properties": {"connector": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "connector.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "dashboard.create": {"description": "Event: dashboard.create", "properties": {"dashboard": {"type": "string"}, "dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.dashboard": {"type": "string"}, "relations.dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "dashboard.show": {"description": "Event: dashboard.show", "properties": {"Dashboard": {"type": "string"}, "Dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "dashboard": {"type": "string"}, "dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}}}, "dashboard.update": {"description": "Event: dashboard.update", "properties": {"dashboard": {"type": "string"}, "dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "dashboard.view.update": {"description": "Event: dashboard.view.update", "properties": {"dashboard": {"type": "string"}, "dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.dashboard": {"type": "string"}, "relations.dashboard.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "device.command.create": {"description": "Event: device.command.create", "properties": {"command_execution": {"type": "string"}, "command_execution.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device.type_id": {"type": "integer", "range": {"min": 1000, "max": 9999}, "description": "Device type ID"}, "device.type_name": {"type": "string", "description": "Device type name", "options": ["Temperature Sensor", "Pressure Sensor", "Flow Meter", "pH Sensor", "Conductivity Meter", "Turbidity Sensor", "Level Sensor", "Valve Controller", "Pump Controller", "Data Logger"]}}}, "device.data.export": {"description": "Event: device.data.export", "properties": {"export": {"type": "string"}, "export.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.export": {"type": "string"}, "relations.export.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device.type_id": {"type": "integer", "range": {"min": 1000, "max": 9999}, "description": "Device type ID"}, "device.type_name": {"type": "string", "description": "Device type name", "options": ["Temperature Sensor", "Pressure Sensor", "Flow Meter", "pH Sensor", "Conductivity Meter", "Turbidity Sensor", "Level Sensor", "Valve Controller", "Pump Controller", "Data Logger"]}}}, "export.show": {"description": "Event: export.show", "properties": {"Export": {"type": "string"}, "Export.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "export": {"type": "string"}, "export.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}}}, "group.create": {"description": "Event: group.create", "properties": {"group": {"type": "string"}, "group.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "name": {"type": "string", "description": "Group name", "options": ["Development Team", "QA Team", "Operations Team", "Analytics Team", "Admin Group", "User Group", "Test Group", "Production Team"]}}}, "group.update": {"description": "Event: group.update", "properties": {"group": {"type": "string"}, "group.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "name": {"type": "string", "description": "Group name", "options": ["Development Team", "QA Team", "Operations Team", "Analytics Team", "Admin Group", "User Group", "Test Group", "Production Team"]}}}, "measurement.create": {"description": "Event: measurement.create", "properties": {"device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "end_at": {"type": "datetime", "format": "iso"}, "measurement": {"type": "string"}, "measurement.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "start_at": {"type": "datetime", "format": "iso"}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "notification.template.create": {"description": "Event: notification.template.create", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "protonotification": {"type": "string"}, "protonotification.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "notification.template.update": {"description": "Event: notification.template.update", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "protonotification": {"type": "string"}, "protonotification.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "notification.trigger.update": {"description": "Event: notification.trigger.update", "properties": {"notification_trigger": {"type": "string"}, "notification_trigger.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "port.binding.create": {"description": "Event: port.binding.create", "properties": {"connector": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "connector.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "port": {"type": "string"}, "port.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.connector": {"type": "string"}, "relations.connector.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.device": {"type": "string"}, "relations.device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.port": {"type": "string"}, "relations.port.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device.type_id": {"type": "integer", "range": {"min": 1000, "max": 9999}, "description": "Device type ID"}, "device.type_name": {"type": "string", "description": "Device type name", "options": ["Temperature Sensor", "Pressure Sensor", "Flow Meter", "pH Sensor", "Conductivity Meter", "Turbidity Sensor", "Level Sensor", "Valve Controller", "Pump Controller", "Data Logger"]}}}, "port.binding.destroy": {"description": "Event: port.binding.destroy", "properties": {"connector": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "connector.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "port": {"type": "string"}, "port.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "status": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "device.type_id": {"type": "integer", "range": {"min": 1000, "max": 9999}, "description": "Device type ID"}, "device.type_name": {"type": "string", "description": "Device type name", "options": ["Temperature Sensor", "Pressure Sensor", "Flow Meter", "pH Sensor", "Conductivity Meter", "Turbidity Sensor", "Level Sensor", "Valve Controller", "Pump Controller", "Data Logger"]}}}, "role.create": {"description": "Event: role.create", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "role": {"type": "string"}, "role.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "name": {"type": "string", "description": "Role name", "options": ["Admin", "User", "Manager", "Viewer", "Editor", "Operator", "Developer", "Analyst"]}}}, "role.update": {"description": "Event: role.update", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "role": {"type": "string"}, "role.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "name": {"type": "string", "description": "Role name", "options": ["Admin", "User", "Manager", "Viewer", "Editor", "Operator", "Developer", "Analyst"]}}}, "secret.create": {"description": "Event: secret.create", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "secret": {"type": "string"}, "secret.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "secret.update": {"description": "Event: secret.update", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "secret": {"type": "string"}, "secret.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "webhook_subscription.create": {"description": "Event: webhook_subscription.create", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "webhook_subscription": {"type": "string"}, "webhook_subscription.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "webhook_subscription.update": {"description": "Event: webhook_subscription.update", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "webhook_subscription": {"type": "string"}, "webhook_subscription.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.comment.create": {"description": "Event: workflow_run.comment.create", "properties": {"comment": {"type": "string"}, "comment.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_step": {"type": "string"}, "workflow_step.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.comment.update": {"description": "Event: workflow_run.comment.update", "properties": {"comment": {"type": "string"}, "comment.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_step": {"type": "string"}, "workflow_step.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.create": {"description": "Event: workflow_run.create", "properties": {"device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_run": {"type": "string"}, "relations.workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_template": {"type": "string"}, "relations.workflow_template.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "state": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_template": {"type": "string"}, "workflow_template.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.create.blank": {"description": "Event: workflow_run.create.blank", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "state": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.field.update": {"description": "Event: workflow_run.field.update", "properties": {"device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_field": {"type": "string"}, "relations.workflow_field.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_run": {"type": "string"}, "relations.workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_step": {"type": "string"}, "relations.workflow_step.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_field": {"type": "string"}, "workflow_field.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_step": {"type": "string"}, "workflow_step.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.signature.create": {"description": "Event: workflow_run.signature.create", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.status.active": {"description": "Event: workflow_run.status.active", "properties": {"device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_run": {"type": "string"}, "relations.workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "state": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.status.completed": {"description": "Event: workflow_run.status.completed", "properties": {"device": {"type": "integer", "range": {"min": 1000, "max": 9999}}, "device.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations": {"type": "string"}, "relations.user": {"type": "string"}, "relations.user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "relations.workflow_run": {"type": "string"}, "relations.workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "state": {"type": "string", "options": ["active", "inactive", "pending", "completed", "failed", "success", "draft"]}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_run.step.complete_substep": {"description": "Event: workflow_run.step.complete_substep", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_run": {"type": "string"}, "workflow_run.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_step": {"type": "string"}, "workflow_step.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_template.create": {"description": "Event: workflow_template.create", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_template": {"type": "string"}, "workflow_template.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}, "workflow_template.destroy": {"description": "Event: workflow_template.destroy", "properties": {"organisation": {"type": "string"}, "organisation.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "timestamp": {"type": "datetime", "format": "iso"}, "user": {"type": "string"}, "user.id": {"type": "integer", "range": {"min": 1, "max": 9999}}, "workflow_template": {"type": "string"}, "workflow_template.id": {"type": "integer", "range": {"min": 1, "max": 9999}}}}}}