import os, requests, datetime, dotenv, json

# Example synthetic events for LO_Web_Test_Labrcp_Roche_v2

dotenv.load_dotenv()

BASE_URL = os.getenv("SEGMENT_BASE_URL", "https://api.segment.io")
WRITE_KEY = os.getenv("SEGMENT_WRITE_KEY")  # required
AUTH = (WRITE_KEY, "")  # Basic Auth: username=write key, password empty

def iso_now():
    return datetime.datetime.utcnow().replace(microsecond=0).isoformat() + "Z"

def track(event, user_id=None, anonymous_id=None, properties=None, timestamp=None):
    if not (user_id or anonymous_id):
        raise ValueError("Provide user_id or anonymous_id")

    # Create the nested properties structure
    nested_properties = {
        "properties": properties or {},
        "user_id": user_id
    }

    payload = {
        "event": event,
        **({"userId": user_id} if user_id else {}),
        **({"anonymousId": anonymous_id} if anonymous_id else {}),
        "timestamp": timestamp or iso_now(),
        "properties": nested_properties
    }
    print(json.dumps(payload, indent=2))
    r = requests.post(f"{BASE_URL}/v1/track", json=payload, auth=AUTH, timeout=10)
    r.raise_for_status()
    return r.status_code

if __name__ == "__main__":
    print("Sending track…")
    print(track(
        event="Device Dashboard View",
        user_id="30bcffa6-ec64-4925-9a1e-f94429164e3f",
        properties={
            "device": 3804,
            "durationSeconds": 3,
            "end": "2025-08-21T09:58:31.739Z",
            "organization": {
                "id": 32
            },
            "pageName": "Device Dashboard",
            "start": "2025-08-21T09:58:29.077Z",
            "timestamp": "2025-08-21T09:58:31.739Z"
        },
    ))
    print("Done.")
