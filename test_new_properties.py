#!/usr/bin/env python3
"""
Test script to verify new properties are being generated correctly
"""

import json
from app import load_events_schema, build_properties_from_schema

def test_role_events():
    """Test role events have name property"""
    schema = load_events_schema("event_schema.json")
    
    print("🧪 Testing role.create event:")
    if "role.create" in schema:
        role_create_props = build_properties_from_schema(schema["role.create"])
        print(json.dumps(role_create_props, indent=2))
        
        # Check if name property exists
        if "name" in role_create_props:
            print("✅ 'name' property found in role.create")
        else:
            print("❌ 'name' property missing in role.create")
    
    print("\n🧪 Testing role.update event:")
    if "role.update" in schema:
        role_update_props = build_properties_from_schema(schema["role.update"])
        print(json.dumps(role_update_props, indent=2))
        
        # Check if name property exists
        if "name" in role_update_props:
            print("✅ 'name' property found in role.update")
        else:
            print("❌ 'name' property missing in role.update")

def test_group_events():
    """Test group events have name property"""
    schema = load_events_schema("event_schema.json")
    
    print("\n🧪 Testing group.create event:")
    if "group.create" in schema:
        group_create_props = build_properties_from_schema(schema["group.create"])
        print(json.dumps(group_create_props, indent=2))
        
        # Check if name property exists
        if "name" in group_create_props:
            print("✅ 'name' property found in group.create")
        else:
            print("❌ 'name' property missing in group.create")

def test_device_events():
    """Test device events have type_id and type_name properties"""
    schema = load_events_schema("event_schema.json")
    
    print("\n🧪 Testing port.binding.create event:")
    if "port.binding.create" in schema:
        port_binding_props = build_properties_from_schema(schema["port.binding.create"])
        print(json.dumps(port_binding_props, indent=2))
        
        # Check if device has type_id and type_name
        if "device" in port_binding_props and isinstance(port_binding_props["device"], dict):
            if "type_id" in port_binding_props["device"]:
                print("✅ 'device.type_id' property found in port.binding.create")
            else:
                print("❌ 'device.type_id' property missing in port.binding.create")
                
            if "type_name" in port_binding_props["device"]:
                print("✅ 'device.type_name' property found in port.binding.create")
            else:
                print("❌ 'device.type_name' property missing in port.binding.create")
        else:
            print("❌ 'device' object structure incorrect in port.binding.create")

if __name__ == "__main__":
    print("🚀 Testing new properties generation...\n")
    
    test_role_events()
    test_group_events() 
    test_device_events()
    
    print("\n🎉 Test completed!")
